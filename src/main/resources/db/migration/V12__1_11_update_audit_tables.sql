 alter table coa_list_aud
       add column coa_description varchar(255);

    alter table coa_list_aud
       add column coa_description_mod bit;

    alter table coa_list_aud
       add column sign bit;

    alter table coa_list_aud
       add column sign_mod bit;

    alter table coa_list_aud
       add column lvl1_category_id integer;

    alter table coa_list_aud
       add column lvl1category_mod bit;

    create table lvl1_category_aud (
        id integer not null,
        rev integer not null,
        revtype tinyint,
        revend integer,
        revend_time datetime(6),
        category varchar(255),
        category_mod bit,
        last_modified_by varchar(255),
        last_modified_by_mod bit,
        primary key (rev, id)
    ) engine=InnoDB;

    alter table lvl1_category_aud
       add constraint FKgw10yswklobuw1v2t0kqj24y1
       foreign key (rev)
       references metadata_rev_entity (id);

    alter table lvl1_category_aud
       add constraint FKms1ftivsssmlnny1my19shupf
       foreign key (revend)
       references metadata_rev_entity (id);
