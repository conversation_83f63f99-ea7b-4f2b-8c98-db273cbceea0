create table coa_list_aud (coa_id integer not null, rev integer not null, revtype tinyint, client_name varchar(255), coa_description varchar(255), coa_text varchar(255), is_active bit, sign bit, lvl1_category_id integer, primary key (rev, coa_id)) engine=InnoDB;
create table lvl1_category_aud (id integer not null, rev integer not null, revtype tinyint, category varchar(255), created_by varchar(255), created_time datetime(6), last_modified_by varchar(255), last_modified_time datetime(6), primary key (rev, id)) engine=InnoDB;
create table revinfo (rev integer not null auto_increment, revtstmp bigint, primary key (rev)) engine=InnoDB;
alter table coa_list_aud add constraint FKqe0jx40peb67235ixeq6pirs5 foreign key (rev) references revinfo (rev);
alter table lvl1_category_aud add constraint FKd47x4ruqp9snbynpexexbk2de foreign key (rev) references revinfo (rev);
