package com.walnut.vegaspread.coa.audit.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for audit filtering that can be used across different entities and microservices.
 */
public record AuditFilterDto(
        @NotBlank(message = "Field name is required")
        String fieldName,

        Object value,

        @NotNull(message = "Operation is required")
        FilterOperation operation
) {

    /**
     * Validation method to ensure the filter is properly configured.
     */
    public boolean isValid() {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }

        if (operation == null) {
            return false;
        }

        // For operations that require a value
        if (requiresValue(operation) && value == null) {
            return false;
        }

        // For BETWEEN operation, value should be a collection with exactly 2 elements
        if (operation == FilterOperation.BETWEEN) {
            if (!(value instanceof java.util.Collection<?> collection) || collection.size() != 2) {
                return false;
            }
        }

        // For IN/NOT_IN operations, value should be a collection
        if ((operation == FilterOperation.IN || operation == FilterOperation.NOT_IN)) {
            return value instanceof java.util.Collection<?> collection && !collection.isEmpty();
        }

        return true;
    }

    /**
     * Check if the operation requires a value.
     */
    private boolean requiresValue(FilterOperation operation) {
        return operation != FilterOperation.IS_NULL && operation != FilterOperation.IS_NOT_NULL;
    }

    /**
     * Supported filter operations.
     */
    public enum FilterOperation {
        EQUALS,
        NOT_EQUALS,
        LIKE,
        IN,
        NOT_IN,
        BETWEEN,
        GREATER_THAN,
        GREATER_THAN_OR_EQUAL,
        LESS_THAN,
        LESS_THAN_OR_EQUAL,
        IS_NULL,
        IS_NOT_NULL
    }

    /**
     * Type of field being filtered.
     */
    public enum FilterType {
        ENTITY_PROPERTY,    // Filter on entity fields
        REVISION_PROPERTY   // Filter on revision fields (username, timestamp only)
    }
}
