package com.walnut.vegaspread.coa.audit.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for audit sorting that can be used across different entities and microservices.
 */
public record AuditSortDto(
        @NotBlank(message = "Field name is required")
        String fieldName,

        @NotNull(message = "Sort direction is required")
        SortDirection direction
) {

    /**
     * Validation method to ensure the sort is properly configured.
     */
    public boolean isValid() {
        return fieldName != null && !fieldName.trim().isEmpty() && direction != null;
    }

    /**
     * Sort direction.
     */
    public enum SortDirection {
        ASC,
        DESC
    }

    /**
     * Type of field being sorted.
     */
    public enum SortType {
        ENTITY_PROPERTY,    // Sort by entity fields
        REVISION_PROPERTY   // Sort by revision fields (only timestamp allowed)
    }

    public static class Builder {
        // Common sorting pattern
        public static AuditSortDto byRevisionDateDesc() {
            return new AuditSortDto("timestamp", SortDirection.DESC);
        }
    }
}
