package com.walnut.vegaspread.coa.audit.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * DTO for audit requests that can be used across different entities and microservices.
 * This provides a flexible way to request audit data with filtering, sorting, and pagination.
 */
public record AuditRequestDto(
        @Valid
        List<AuditFilterDto> filters,

        @Valid
        List<AuditSortDto> sorts,

        @NotNull(message = "Page number is required")
        @Min(value = 1, message = "Page number must be at least 1")
        Integer pageNumber,

        @NotNull(message = "Page size is required")
        @Min(value = 1, message = "Page size must be at least 1")
        @Max(value = 1000, message = "Page size cannot exceed 1000")
        Integer pageSize
) {

    /**
     * Validation method to ensure the request is properly configured.
     */
    public boolean isValid() {
        if (pageNumber == null || pageNumber < 1) {
            return false;
        }

        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return false;
        }

        // Validate all filters
        if (filters != null) {
            for (AuditFilterDto filter : filters) {
                if (!filter.isValid()) {
                    return false;
                }
            }
        }

        // Validate all sorts
        if (sorts != null) {
            for (AuditSortDto sort : sorts) {
                if (!sort.isValid()) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Get filters with null safety.
     */
    public List<AuditFilterDto> getFilters() {
        return filters != null ? filters : List.of();
    }

    /**
     * Get sorts with null safety and default sorting.
     */
    public List<AuditSortDto> getSorts() {
        if (sorts == null || sorts.isEmpty()) {
            return List.of(AuditSortDto.Builder.byRevisionDateDesc());
        }
        return sorts;
    }
}
