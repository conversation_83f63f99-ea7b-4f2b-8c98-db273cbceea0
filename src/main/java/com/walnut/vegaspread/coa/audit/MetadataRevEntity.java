package com.walnut.vegaspread.coa.audit;

import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;

@Getter
@Setter
@Entity
@RevisionEntity(MetadataRevListener.class)
public class MetadataRevEntity extends DefaultRevisionEntity {
    private String username;
    private String traceId;
}
