package com.walnut.vegaspread.coa.audit;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanContext;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.spi.CDI;
import org.hibernate.envers.RevisionListener;

import java.util.UUID;

@ApplicationScoped
public class MetadataRevListener implements RevisionListener {

    UserContextService userContextService;

    public MetadataRevListener() {
        this.userContextService = CDI.current().select(UserContextService.class).get();
    }

    @Override
    public void newRevision(Object revisionEntity) {
        MetadataRevEntity revEntity = (MetadataRevEntity) revisionEntity;
        revEntity.setUsername(userContextService.getCurrentUsername());
        SpanContext spanContext = Span.current().getSpanContext();
        revEntity.setTraceId(spanContext.isValid() ? spanContext.getTraceId() : "fallback-" + UUID.randomUUID());
    }
}