package com.walnut.vegaspread.coa.service;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUpdateUploadBean;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;

@ApplicationScoped
public class CoaService {

    private static final Logger logger = Logger.getLogger(CoaService.class);
    private final JsonWebToken accessToken;
    private final CoaRepository coaRepository;
    private final ExchangeService exchangeService;
    private final Lvl1CategoryService lvl1CategoryService;

    public CoaService(JsonWebToken accessToken, CoaRepository coaRepository, ExchangeService exchangeService,
                      Lvl1CategoryService lvl1CategoryService) {
        this.accessToken = accessToken;
        this.coaRepository = coaRepository;
        this.exchangeService = exchangeService;
        this.lvl1CategoryService = lvl1CategoryService;
    }

    private static List<CoaEntityDto.Update> updateUploadBeansToUpdateDtos(
            List<CoaUpdateUploadBean> coaUpdateUploadBeans,
            Map<String, Lvl1CategoryEntity> lvl1CategoryEntityMap) {

        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        for (CoaUpdateUploadBean coaUpdateUploadBean : coaUpdateUploadBeans) {

            Lvl1CategoryEntity lvl1CategoryEntity = lvl1CategoryEntityMap.get(
                    coaUpdateUploadBean.getLvl1Category());
            if (lvl1CategoryEntity == null) {
                logger.errorf("Lvl1 category %s not found", coaUpdateUploadBean.getLvl1Category());
                return ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                        "Lvl1 category " + coaUpdateUploadBean.getLvl1Category() + " not found");
            } else {

                coaEntityUpdateDtos.add(new CoaEntityDto.Update(
                        coaUpdateUploadBean.getCoaId(),
                        Optional.of(coaUpdateUploadBean.getCoaText()),
                        Optional.of(coaUpdateUploadBean.getCoaDescription()),
                        Optional.of(lvl1CategoryEntity.getId()),
                        Optional.of(coaUpdateUploadBean.getIsActive()),
                        Optional.of(coaUpdateUploadBean.getSign()))
                );
            }
        }
        return coaEntityUpdateDtos;
    }

    @Transactional
    public List<CoaEntity> create(List<CoaEntityDto.Create> coaEntityCreateDtos, String clientName)
            throws NullPointerException {
        if (clientName == null) {
            logger.errorf("Client name is null for COA creation");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Client name cannot be null");
        }
        List<CoaEntity> coaEntities = coaEntityCreateDtos.stream()
                .map(coaEntityCreateDto -> {
                    Lvl1CategoryEntity lvl1CategoryEntity = lvl1CategoryService.findById(
                            coaEntityCreateDto.lvl1CategoryId());
                    if (lvl1CategoryEntity == null) {
                        logger.errorf("Lvl1 category with id %s not found", coaEntityCreateDto.lvl1CategoryId());
                        return ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                                "Lvl1 category with id " + coaEntityCreateDto.lvl1CategoryId() + " not found");
                    }
                    return CoaEntity.builder()
                            .coaText(coaEntityCreateDto.coaText())
                            .coaDescription(coaEntityCreateDto.coaDescription())
                            .lvl1Category(lvl1CategoryEntity)
                            .isActive(coaEntityCreateDto.isActive())
                            .clientName(clientName)
                            .sign(coaEntityCreateDto.sign())
                            .build();
                })
                .toList();
        coaRepository.persist(coaEntities);
        List<CoaItemAuditDto.Create> coaItemCreateAudits = coaEntities.stream()
                .flatMap(coaEntity -> Stream.of(
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                                coaEntity.isActive.toString())
                ))
                .toList();
        exchangeService.coaItemAuditForCreate(coaItemCreateAudits);
        return coaEntities;
    }

    @Transactional
    public List<CoaEntity> update(List<CoaEntityDto.Update> coaEntityUpdateDtos) {
        if (coaEntityUpdateDtos == null || coaEntityUpdateDtos.isEmpty()) {
            return List.of();
        }
        List<CoaEntity> updatedCoaEntities = new ArrayList<>();
        List<CoaItemAuditDto.Update> coaItemRequests = new ArrayList<>();
        for (CoaEntityDto.Update coaEntityUpdateDto : coaEntityUpdateDtos) {
            CoaEntity coaEntity = coaRepository.findById(coaEntityUpdateDto.coaId());
            if (coaEntity == null) {
                logger.errorf("No COA found with id %s. Updated failed.", coaEntityUpdateDto.coaId());
            } else {
                coaEntityUpdateDto.coaText().flatMap(newCoaText -> Optional.ofNullable(
                        updateFieldAndAudit(newCoaText, coaEntity::getCoaText, coaEntity::setCoaText,
                                CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaId))).ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.coaDescription()
                        .ifPresent(newCoaDescription -> updateFieldIfChanged(newCoaDescription,
                                coaEntity::getCoaDescription, coaEntity::setCoaDescription));

                coaEntityUpdateDto.isActive()
                        .flatMap(newIsActive -> Optional.ofNullable(
                                updateFieldAndAudit(newIsActive, coaEntity::getIsActive,
                                        coaEntity::setIsActive,
                                        CoaEntity.IS_ACTIVE_COL_NAME, coaEntity.getCoaId())))
                        .ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.sign()
                        .flatMap(newSign -> Optional.ofNullable(
                                updateFieldAndAudit(newSign, coaEntity::getSign,
                                        coaEntity::setSign,
                                        CoaEntity.SIGN_COL_NAME, coaEntity.getCoaId())))
                        .ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.lvl1CategoryId()
                        .ifPresent(newLvl1CategoryId -> {
                            Lvl1CategoryEntity lvl1CategoryEntity = lvl1CategoryService.findById(
                                    newLvl1CategoryId);
                            if (lvl1CategoryEntity == null) {
                                logger.errorf("Lvl1 category with id %s not found", newLvl1CategoryId);
                                ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                                        "Lvl1 category with id " + newLvl1CategoryId + " not found");
                            } else {
                                updateFieldIfChanged(lvl1CategoryEntity, coaEntity::getLvl1Category,
                                        coaEntity::setLvl1Category);
                            }
                        });

                updatedCoaEntities.add(coaEntity);
            }
        }
        exchangeService.coaItemAuditForUpdate(coaItemRequests);
        coaRepository.persist(updatedCoaEntities);
        return updatedCoaEntities;
    }

    private <T> void updateFieldIfChanged(T newValue, Supplier<T> getter, Consumer<T> setter) {
        if (!newValue.equals(getter.get())) {
            setter.accept(newValue);
        }
    }

    private <T> CoaItemAuditDto.Update updateFieldAndAudit(T newValue, Supplier<T> getter, Consumer<T> setter,
                                                           String fieldName, Integer coaId) {
        T oldValue = getter.get();
        if (!newValue.equals(oldValue)) {
            setter.accept(newValue);
            return new CoaItemAuditDto.Update(coaId, fieldName,
                    oldValue.toString(), newValue.toString());
        }
        return null;
    }

    public List<CoaEntity> list(String clientName, Boolean onlyActive) throws NullPointerException {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        List<CoaEntity> coaList = Boolean.TRUE.equals(onlyActive) ? coaRepository.listActiveForClient(finalClientName)
                : coaRepository.listAllForClient(finalClientName);
        List<CoaEntity> commonCoaList = coaRepository.listAllForClient("common");
        commonCoaList.addAll(coaList);
        return commonCoaList;
    }

    public List<CoaEntity> listWithSortAndSearch(CoaListDto.GetSortAndSearchCoaList sortAndSearchDto, String clientName,
                                                 Boolean onlyActive) {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        List<CoaEntity> coaList = Boolean.TRUE.equals(onlyActive) ? coaRepository.listActiveForClient(finalClientName,
                sortAndSearchDto)
                : coaRepository.listAllForClient(finalClientName, sortAndSearchDto);
        List<CoaEntity> commonCoaList = coaRepository.listAllForClient("common", sortAndSearchDto);
        coaList.addAll(commonCoaList);
        return coaList;
    }

    public void delete(Integer coaId) {
        CoaEntity coaEntity = coaRepository.findById(coaId);
        if (coaEntity == null) {
            logger.errorf("No COA found with id %s. Delete failed.", coaId);
            ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                    "No COA found with id " + coaId);
        }
        List<CoaItemAuditDto.Delete> coaItemRequests = List.of(
                new CoaItemAuditDto.Delete(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                new CoaItemAuditDto.Delete(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                        coaEntity.isActive.toString()));
        coaEntity.isActive = Boolean.FALSE;
        save(coaEntity);
        exchangeService.coaItemAuditForDelete(coaItemRequests);
    }

    @Transactional
    public void save(CoaEntity coaEntity) {
        coaRepository.persist(coaEntity);
    }

    @Transactional
    public List<CoaUploadBean> uploadCsv(FileUpload file) throws IOException {

        List<CoaUploadBean> coaUploadBeans;
        try (MappingIterator<CoaUploadBean> coaUploadBeanIterator = new CsvMapper().readerFor(CoaUploadBean.class)
                .with(CsvSchema.emptySchema().withHeader())
                .readValues(file.filePath().toFile())) {
            coaUploadBeans = coaUploadBeanIterator.readAll();
        }
        List<String> lvl1Categories = coaUploadBeans.stream()
                .map(CoaUploadBean::getLvl1Category)
                .distinct()
                .toList();
        Map<String, Lvl1CategoryEntity> lvl1CategoryEntityMap = lvl1CategoryService.findByCategories(lvl1Categories)
                .stream()
                .collect(Collectors.toMap(Lvl1CategoryEntity::getCategory, lvl1CategoryEntity -> lvl1CategoryEntity));

        List<CoaEntity> coaEntities = coaUploadBeans.stream().map(coaUploadBean -> {
            Lvl1CategoryEntity lvl1CategoryEntity = lvl1CategoryEntityMap.get(coaUploadBean.getLvl1Category());
            if (lvl1CategoryEntity == null) {
                logger.errorf("Lvl1 category %s not found", coaUploadBean.getLvl1Category());
                return ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                        "Lvl1 category " + coaUploadBean.getLvl1Category() + " not found");
            }
            return CoaEntity.builder()
                    .coaText(coaUploadBean.getCoaText())
                    .coaDescription(coaUploadBean.getCoaDescription())
                    .clientName(coaUploadBean.getClientName())
                    .lvl1Category(lvl1CategoryEntity)
                    .isActive(coaUploadBean.getIsActive())
                    .sign(coaUploadBean.getSign()).build();
        }).toList();

        coaRepository.persist(coaEntities);

        List<CoaItemAuditDto.Create> coaItemRequests = coaEntities.stream()
                .flatMap(coaEntity -> Stream.of(
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                                coaEntity.isActive.toString())
                ))
                .toList();
        exchangeService.coaItemAuditForCreate(coaItemRequests);

        return coaUploadBeans;
    }

    public List<CoaItemDto> get(List<Integer> coaIds) {
        if (coaIds == null || coaIds.isEmpty()) {
            return Collections.emptyList();
        }
        return CoaEntity.toDtoList(coaRepository.findByIds(coaIds));
    }

    @Transactional
    public List<CoaEntity> uploadUpdateCsv(FileUpload file) throws IOException {

        List<CoaUpdateUploadBean> coaUpdateUploadBeans;
        try (MappingIterator<CoaUpdateUploadBean> coaUpdateUploadBeanIterator = new CsvMapper().readerFor(
                        CoaUpdateUploadBean.class)
                .with(CsvSchema.emptySchema().withHeader())
                .readValues(file.filePath().toFile())) {

            coaUpdateUploadBeans = coaUpdateUploadBeanIterator.readAll();
        }

        Set<Integer> updateCoaIds = coaUpdateUploadBeans.stream()
                .map(CoaUpdateUploadBean::getCoaId)
                .collect(Collectors.toSet());

        List<CoaEntity> coaEntities = coaRepository.findByIds(new ArrayList<>(updateCoaIds));

        if (coaEntities.size() != coaUpdateUploadBeans.size()) {
            Set<Integer> existingCoaIds = coaEntities.stream().map(CoaEntity::getCoaId).collect(Collectors.toSet());
            updateCoaIds.removeAll(existingCoaIds);
            ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                    "Missing coa id/s for update: " + updateCoaIds);
        }

        Map<String, Lvl1CategoryEntity> lvl1CategoryEntityMap = lvl1CategoryService.findByCategories(
                        coaUpdateUploadBeans.stream()
                                .map(CoaUpdateUploadBean::getLvl1Category)
                                .distinct()
                                .toList())
                .stream()
                .collect(Collectors.toMap(Lvl1CategoryEntity::getCategory, lvl1CategoryEntity -> lvl1CategoryEntity));
        List<CoaEntityDto.Update> coaEntityUpdateDtos = updateUploadBeansToUpdateDtos(coaUpdateUploadBeans,
                lvl1CategoryEntityMap);

        return update(coaEntityUpdateDtos);
    }
}
