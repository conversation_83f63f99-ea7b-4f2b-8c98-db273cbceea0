package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.audit.AuditFieldDetector;
import com.walnut.vegaspread.coa.audit.BaseEntityMapper;
import com.walnut.vegaspread.coa.audit.EntityRevisionWithChanges;
import com.walnut.vegaspread.coa.audit.dto.AuditFilterDto;
import com.walnut.vegaspread.coa.audit.dto.AuditRequestDto;
import com.walnut.vegaspread.coa.audit.dto.AuditSortDto;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.ws.rs.core.Response;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.hibernate.envers.query.criteria.MatchMode;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Generic audit service that uses DTOs for filtering and sorting.
 * This service is completely decoupled from specific entities and can be used
 * across different entities and microservices.
 */
@ApplicationScoped
public class CoaAuditService {
    private static final Logger logger = Logger.getLogger(CoaAuditService.class);

    private final AuditFieldDetector fieldDetector;

    public CoaAuditService(AuditFieldDetector fieldDetector) {
        this.fieldDetector = fieldDetector;
    }

    /**
     * Validate an audit request for field restrictions.
     * Simple validation that only checks if fields are allowed for filtering/sorting.
     */
    public <E> List<String> validateRequest(Class<E> entityClass, AuditRequestDto request,
                                            EntityManager entityManager) {
        List<String> errors = new java.util.ArrayList<>();

        if (!request.isValid()) {
            errors.add("Basic request validation failed");
            return errors;
        }

        // Validate filters - only check if fields are allowed
        if (request.getFilters() != null) {
            for (AuditFilterDto filter : request.getFilters()) {
                if (!fieldDetector.isFilteringAllowed(entityClass, filter.fieldName(), entityManager)) {
                    errors.add(String.format(
                            "Filtering is not allowed on field '%s'. Only audited columns, username, and timestamp " +
                                    "are allowed for filtering.",
                            filter.fieldName()));
                }
            }
        }

        // Validate sorts - only check if fields are allowed
        if (request.getSorts() != null) {
            for (AuditSortDto sort : request.getSorts()) {
                if (!fieldDetector.isSortingAllowed(entityClass, sort.fieldName(), entityManager)) {
                    errors.add(String.format(
                            "Sorting is not allowed on field '%s'. Only entity columns and timestamp are allowed for " +
                                    "sorting.",
                            sort.fieldName()));
                }
            }
        }

        return errors;
    }

    /**
     * Get audits using DTO-based filtering and sorting.
     * This method is completely generic and can work with any entity.
     *
     * @param entityClass   The entity class
     * @param request       The audit request with filters, sorts, and pagination
     * @param entityManager The entity manager
     * @param <E>           The entity type
     * @return List of audit revisions
     * @throws IllegalArgumentException if the request contains invalid field type operations
     */
    public <E> List<EntityRevisionWithChanges<E>> getAudits(
            Class<E> entityClass, AuditRequestDto request, EntityManager entityManager) {

        // Validate request (includes both basic validation and field type compatibility)
        List<String> validationErrors = validateRequest(entityClass, request, entityManager);
        if (!validationErrors.isEmpty()) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Request validation failed: " + String.join(", ", validationErrors));
        }

        AuditReader reader = AuditReaderFactory.get(entityManager);
        AuditQuery query = reader.createQuery().forRevisionsOfEntityWithChanges(entityClass, true);

        // Apply filters with automatic type detection
        applyFilters(query, request.getFilters(), entityClass, entityManager);

        // Apply sorting with automatic type detection
        applySorting(query, request.getSorts(), entityClass, entityManager);

        // Apply pagination
        int offset = (request.pageNumber() - 1) * request.pageSize();

        @SuppressWarnings("unchecked")
        List<EntityRevisionWithChanges<E>> results = query
                .setFirstResult(offset)
                .setMaxResults(request.pageSize())
                .getResultList()
                .stream()
                .map(result -> EntityRevisionWithChanges.fromArray(result, entityClass))
                .toList();

        return results;
    }

    /**
     * Get count of audits using DTO-based filtering.
     *
     * @param entityClass   The entity class
     * @param filters       The filters to apply
     * @param entityManager The entity manager
     * @param <E>           The entity type
     * @return Count of matching audits
     */
    public <E> long getAuditCount(Class<E> entityClass, List<AuditFilterDto> filters, EntityManager entityManager) {
        AuditReader reader = AuditReaderFactory.get(entityManager);
        AuditQuery query = reader.createQuery().forRevisionsOfEntityWithChanges(entityClass, true);

        // Apply filters with automatic type detection
        if (filters != null) {
            applyFilters(query, filters, entityClass, entityManager);
        }

        // Use native projection for counting
        Number count = (Number) query
                .addProjection(AuditEntity.revisionNumber().countDistinct())
                .getSingleResult();

        return count.longValue();
    }

    /**
     * Get paginated response with count and map entities to DTOs.
     *
     * @param entityClass   The entity class
     * @param request       The audit request
     * @param mapper        The mapper to convert entities to DTOs
     * @param entityManager The entity manager
     * @param <E>           The entity type
     * @param <D>           The DTO type
     * @return Paginated response with DTOs
     */
    public <E, D> PaginatedAuditResponse<AuditRevisionDto<D>> getPaginatedAuditsAsDto(
            Class<E> entityClass, AuditRequestDto request, BaseEntityMapper<E, D> mapper, EntityManager entityManager) {

        // Get the entity data
        List<EntityRevisionWithChanges<E>> entityItems = getAudits(entityClass, request, entityManager);

        // Map entities to DTOs
        List<E> entities = entityItems.stream()
                .map(EntityRevisionWithChanges::entity)
                .toList();

        List<D> dtos = mapper.toDtoList(entities);

        List<AuditRevisionDto<D>> dtoItems = new ArrayList<>(entityItems.size());
        for (int i = 0; i < entityItems.size(); i++) {
            EntityRevisionWithChanges<E> rev = entityItems.get(i);
            D dto = dtos.get(i);
            dtoItems.add(new AuditRevisionDto<>(
                    dto,
                    rev.revisionEntity(),
                    rev.revisionType(),
                    rev.modifiedProperties()
            ));
        }
        // Get the count
        long totalElements = getAuditCount(entityClass, request.getFilters(), entityManager);

        int totalPages = (int) Math.ceil((double) totalElements / request.pageSize());

        return new PaginatedAuditResponse<>(
                totalElements, totalPages, request.pageNumber(), request.pageSize(), dtoItems);
    }

    /**
     * Apply filters to the audit query.
     * Automatically detects filter types based on field names and entity class.
     */
    private void applyFilters(AuditQuery query, List<AuditFilterDto> filters, Class<?> entityClass,
                              EntityManager entityManager) {
        if (filters == null || filters.isEmpty()) {
            return;
        }

        for (AuditFilterDto filter : filters) {

            // Simple type detection for allowed fields only
            AuditFilterDto.FilterType detectedType = fieldDetector.detectFilterType(
                    filter.fieldName());

            // Apply filter based on detected type (only allowed types)
            switch (detectedType) {
                case ENTITY_PROPERTY -> applyEntityPropertyFilter(query, filter);
                case REVISION_PROPERTY -> applyRevisionPropertyFilter(query, filter);
            }
        }
    }

    /**
     * Apply entity property filters.
     */
    private void applyEntityPropertyFilter(AuditQuery query, AuditFilterDto filter) {
        switch (filter.operation()) {
            case EQUALS -> query.add(AuditEntity.property(filter.fieldName()).eq(filter.value()));
            case NOT_EQUALS -> query.add(AuditEntity.property(filter.fieldName()).ne(filter.value()));
            case LIKE -> query.add(
                    AuditEntity.property(filter.fieldName()).like((String) filter.value(), MatchMode.ANYWHERE));
            case IN -> query.add(AuditEntity.property(filter.fieldName()).in((Collection<?>) filter.value()));
            case NOT_IN -> {
                for (Object value : (Collection<?>) filter.value()) {
                    query.add(AuditEntity.property(filter.fieldName()).ne(value));
                }
            }
            case BETWEEN -> {
                List<?> values = (List<?>) filter.value();
                query.add(AuditEntity.property(filter.fieldName()).between(values.get(0), values.get(1)));
            }
            case GREATER_THAN -> query.add(AuditEntity.property(filter.fieldName()).gt(filter.value()));
            case GREATER_THAN_OR_EQUAL -> query.add(AuditEntity.property(filter.fieldName()).ge(filter.value()));
            case LESS_THAN -> query.add(AuditEntity.property(filter.fieldName()).lt(filter.value()));
            case LESS_THAN_OR_EQUAL -> query.add(AuditEntity.property(filter.fieldName()).le(filter.value()));
            case IS_NULL -> query.add(AuditEntity.property(filter.fieldName()).isNull());
            case IS_NOT_NULL -> query.add(AuditEntity.property(filter.fieldName()).isNotNull());
        }
    }

    /**
     * Apply revision property filters.
     */
    private void applyRevisionPropertyFilter(AuditQuery query, AuditFilterDto filter) {
        switch (filter.operation()) {
            case EQUALS -> query.add(AuditEntity.revisionProperty(filter.fieldName()).eq(filter.value()));
            case NOT_EQUALS -> query.add(AuditEntity.revisionProperty(filter.fieldName()).ne(filter.value()));
            case LIKE -> query.add(AuditEntity.revisionProperty(filter.fieldName()).like("%" + filter.value() + "%"));
            case IN -> query.add(AuditEntity.revisionProperty(filter.fieldName()).in((Collection<?>) filter.value()));
            case NOT_IN -> {
                for (Object value : (Collection<?>) filter.value()) {
                    query.add(AuditEntity.revisionProperty(filter.fieldName()).ne(value));
                }
            }
            case BETWEEN -> {
                List<?> values = (List<?>) filter.value();
                query.add(AuditEntity.revisionProperty(filter.fieldName()).between(values.get(0), values.get(1)));
            }
            case GREATER_THAN -> query.add(AuditEntity.revisionProperty(filter.fieldName()).gt(filter.value()));
            case GREATER_THAN_OR_EQUAL ->
                    query.add(AuditEntity.revisionProperty(filter.fieldName()).ge(filter.value()));
            case LESS_THAN -> query.add(AuditEntity.revisionProperty(filter.fieldName()).lt(filter.value()));
            case LESS_THAN_OR_EQUAL -> query.add(AuditEntity.revisionProperty(filter.fieldName()).le(filter.value()));
        }
    }

    /**
     * Apply sorting to the audit query.
     * Automatically detects sort types based on field names and entity class.
     */
    private void applySorting(AuditQuery query, List<AuditSortDto> sorts, Class<?> entityClass,
                              EntityManager entityManager) {
        if (sorts == null || sorts.isEmpty()) {
            // Default sort by revision number descending
            query.addOrder(AuditEntity.revisionNumber().desc());
            return;
        }

        for (AuditSortDto sort : sorts) {

            // Simple type detection for allowed fields only
            AuditSortDto.SortType detectedType = fieldDetector.detectSortType(
                    sort.fieldName());

            // Apply sorting based on detected type (only allowed types)
            switch (detectedType) {
                case ENTITY_PROPERTY -> {
                    if (sort.direction() == AuditSortDto.SortDirection.ASC) {
                        query.addOrder(AuditEntity.property(sort.fieldName()).asc());
                    } else {
                        query.addOrder(AuditEntity.property(sort.fieldName()).desc());
                    }
                }
                case REVISION_PROPERTY -> {
                    if (sort.direction() == AuditSortDto.SortDirection.ASC) {
                        query.addOrder(AuditEntity.revisionProperty(sort.fieldName()).asc());
                    } else {
                        query.addOrder(AuditEntity.revisionProperty(sort.fieldName()).desc());
                    }
                }
            }
        }
    }

    /**
     * Given the trace id rollback coa entity values to the previous state. The trace id corresponds to the trace id
     * column created using MetadataRevListener
     *
     * @param traceId
     */
    public void rollback(Long traceId) {
        logger.infof("Rolling back trace id %s", traceId);
    }

    /**
     * Paginated response record.
     */
    public record PaginatedAuditResponse<T>(
            long totalElements,
            int totalPages,
            int pageNumber,
            int pageSize,
            List<T> items
    ) {
    }

    /**
     * Audit revision DTO that contains the mapped DTO instead of the entity.
     *
     * @param dto                The mapped DTO
     * @param revisionEntity     The revision metadata
     * @param revisionType       The type of revision (ADD, MOD, DEL)
     * @param modifiedProperties List of modified property names
     * @param <D>                The DTO type
     */
    public record AuditRevisionDto<D>(
            D dto,
            Object revisionEntity,
            org.hibernate.envers.RevisionType revisionType,
            java.util.Set<String> modifiedProperties
    ) {
    }
}
