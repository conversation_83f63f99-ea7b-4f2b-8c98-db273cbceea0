package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.workflow.service.CleanupService;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/cleanup")
@ApiKeyAuthenticate
public class CleanupResource {
    private final CleanupService cleanupService;

    public CleanupResource(CleanupService cleanupService) {
        this.cleanupService = cleanupService;
    }

    @POST
    @Path("/incomplete-jobs")
    public void cleanupIncompleteJobs() {
        cleanupService.cleanupIncompleteJobs();
    }
}
