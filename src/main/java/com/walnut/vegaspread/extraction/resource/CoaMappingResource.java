package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.extraction.model.TableTypeAndFsHeaderDto;
import com.walnut.vegaspread.extraction.service.CoaMappingService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/coa-mapping")
@Authenticated
public class CoaMappingResource {

    private final CoaMappingService coaMappingService;

    public CoaMappingResource(CoaMappingService coaMappingService) {
        this.coaMappingService = coaMappingService;
    }

    @POST
    @Path("/coa-for-tag")
    public List<Integer> getCoaIdsForTableTypeOrFsHeader(TableTypeAndFsHeaderDto tableTypeAndFsHeaderDto) {
        return coaMappingService.getCoaIdsForTableTypeOrFsHeader(tableTypeAndFsHeaderDto.tableType(),
                tableTypeAndFsHeaderDto.fsHeader());
    }
}
